"""
Utility functions for the Hybrid VGG-ViT model.

This module provides helper functions for model analysis, visualization,
and inference utilities.
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Tuple, Dict, Any
import torchvision.transforms as transforms
from PIL import Image
import cv2

from hybrid_vgg_vit import HybridVGGViT


class ModelAnalyzer:
    """
    Utility class for analyzing the Hybrid VGG-ViT model.
    """
    
    def __init__(self, model: HybridVGGViT, device: torch.device):
        self.model = model.to(device)
        self.device = device
        self.model.eval()
        
    def count_parameters(self) -> Dict[str, int]:
        """Count parameters in different parts of the model."""
        vgg_params = 0
        transformer_params = 0
        total_params = 0
        
        for name, param in self.model.named_parameters():
            param_count = param.numel()
            total_params += param_count
            
            if 'vgg_features' in name:
                vgg_params += param_count
            elif any(x in name for x in ['patch_embed', 'transformer_blocks', 'norm', 'head']):
                transformer_params += param_count
        
        return {
            'total': total_params,
            'vgg_features': vgg_params,
            'transformer': transformer_params,
            'vgg_percentage': (vgg_params / total_params) * 100,
            'transformer_percentage': (transformer_params / total_params) * 100
        }
    
    def analyze_model_complexity(self) -> Dict[str, Any]:
        """Analyze model computational complexity."""
        # Count parameters
        param_stats = self.count_parameters()
        
        # Estimate FLOPs (simplified)
        input_tensor = torch.randn(1, 3, 224, 224).to(self.device)
        
        # Memory usage estimation
        model_size_mb = sum(p.numel() * 4 for p in self.model.parameters()) / (1024 * 1024)  # 4 bytes per float32
        
        return {
            'parameters': param_stats,
            'model_size_mb': model_size_mb,
            'input_shape': (3, 224, 224),
            'output_shape': self.model(input_tensor).shape[1:]
        }
    
    def visualize_feature_maps(self, image: torch.Tensor, layer_idx: int = -1) -> np.ndarray:
        """
        Visualize feature maps from VGG blocks.
        
        Args:
            image: Input image tensor (1, 3, H, W)
            layer_idx: Which VGG block to visualize (-1 for last block)
            
        Returns:
            Feature maps as numpy array
        """
        with torch.no_grad():
            # Get feature maps from VGG blocks
            x = image.to(self.device)
            
            # Forward through VGG blocks
            features = []
            x = self.model.vgg_features.block1(x)
            features.append(x)
            x = self.model.vgg_features.block2(x)
            features.append(x)
            x = self.model.vgg_features.block3(x)
            features.append(x)
            x = self.model.vgg_features.block4(x)
            features.append(x)
            
            # Select the requested layer
            selected_features = features[layer_idx]
            
            # Convert to numpy and normalize
            feature_maps = selected_features.cpu().numpy()[0]  # Remove batch dimension
            
            return feature_maps
    
    def plot_feature_maps(self, image: torch.Tensor, num_maps: int = 16, figsize: Tuple[int, int] = (12, 8)):
        """Plot feature maps from the last VGG block."""
        feature_maps = self.visualize_feature_maps(image)
        
        # Select subset of feature maps
        num_maps = min(num_maps, feature_maps.shape[0])
        selected_maps = feature_maps[:num_maps]
        
        # Create subplot grid
        rows = int(np.sqrt(num_maps))
        cols = int(np.ceil(num_maps / rows))
        
        fig, axes = plt.subplots(rows, cols, figsize=figsize)
        axes = axes.flatten() if num_maps > 1 else [axes]
        
        for i, feature_map in enumerate(selected_maps):
            axes[i].imshow(feature_map, cmap='viridis')
            axes[i].set_title(f'Feature Map {i+1}')
            axes[i].axis('off')
        
        # Hide unused subplots
        for i in range(num_maps, len(axes)):
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        return fig
    
    def get_model_summary(self) -> str:
        """Generate a detailed model summary."""
        complexity = self.analyze_model_complexity()
        
        summary = f"""
Hybrid VGG-ViT Model Summary
============================

Architecture Overview:
- VGG Feature Extractor: 4 blocks with increasing channels (64→128→256→512)
- Patch Embedding: Converts 14×14×512 feature maps to patches
- Vision Transformer: {len(self.model.transformer_blocks)} layers with multi-head attention
- Classification Head: Linear layer for final predictions

Parameter Distribution:
- Total Parameters: {complexity['parameters']['total']:,}
- VGG Features: {complexity['parameters']['vgg_features']:,} ({complexity['parameters']['vgg_percentage']:.1f}%)
- Transformer: {complexity['parameters']['transformer']:,} ({complexity['parameters']['transformer_percentage']:.1f}%)

Model Size: {complexity['model_size_mb']:.1f} MB
Input Shape: {complexity['input_shape']}
Output Shape: {complexity['output_shape']}

Architecture Flow:
Input (3×224×224) 
→ VGG Block 1 (64×112×112)
→ VGG Block 2 (128×56×56) 
→ VGG Block 3 (256×28×28)
→ VGG Block 4 (512×14×14)
→ Patch Embedding (50×768) [49 patches + 1 CLS token]
→ Transformer Blocks (50×768)
→ Classification Head ({complexity['output_shape'][0]} classes)
        """
        
        return summary.strip()


class InferenceUtils:
    """
    Utility class for model inference and prediction.
    """
    
    def __init__(self, model: HybridVGGViT, device: torch.device, class_names: List[str] = None):
        self.model = model.to(device)
        self.device = device
        self.model.eval()
        self.class_names = class_names
        
        # Standard ImageNet preprocessing
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def predict_image(self, image_path: str, top_k: int = 5) -> List[Tuple[str, float]]:
        """
        Predict class for a single image.
        
        Args:
            image_path: Path to the image file
            top_k: Number of top predictions to return
            
        Returns:
            List of (class_name, probability) tuples
        """
        # Load and preprocess image
        image = Image.open(image_path).convert('RGB')
        input_tensor = self.transform(image).unsqueeze(0).to(self.device)
        
        # Make prediction
        with torch.no_grad():
            outputs = self.model(input_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            top_probs, top_indices = torch.topk(probabilities, top_k)
        
        # Format results
        results = []
        for i in range(top_k):
            prob = top_probs[0][i].item()
            idx = top_indices[0][i].item()
            
            if self.class_names and idx < len(self.class_names):
                class_name = self.class_names[idx]
            else:
                class_name = f"Class_{idx}"
            
            results.append((class_name, prob))
        
        return results
    
    def batch_predict(self, image_paths: List[str]) -> List[List[Tuple[str, float]]]:
        """Predict classes for multiple images."""
        results = []
        for image_path in image_paths:
            try:
                prediction = self.predict_image(image_path)
                results.append(prediction)
            except Exception as e:
                print(f"Error processing {image_path}: {e}")
                results.append([])
        
        return results
    
    def visualize_prediction(self, image_path: str, top_k: int = 5):
        """Visualize image with top predictions."""
        # Load image
        image = Image.open(image_path).convert('RGB')
        
        # Get predictions
        predictions = self.predict_image(image_path, top_k)
        
        # Create visualization
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Display image
        ax1.imshow(image)
        ax1.set_title('Input Image')
        ax1.axis('off')
        
        # Display predictions
        classes = [pred[0] for pred in predictions]
        probs = [pred[1] for pred in predictions]
        
        y_pos = np.arange(len(classes))
        bars = ax2.barh(y_pos, probs)
        ax2.set_yticks(y_pos)
        ax2.set_yticklabels(classes)
        ax2.set_xlabel('Probability')
        ax2.set_title(f'Top {top_k} Predictions')
        ax2.set_xlim(0, 1)
        
        # Add probability labels on bars
        for i, (bar, prob) in enumerate(zip(bars, probs)):
            ax2.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
                    f'{prob:.3f}', va='center')
        
        plt.tight_layout()
        plt.show()
        
        return fig


def load_model_checkpoint(checkpoint_path: str, model: HybridVGGViT, device: torch.device) -> HybridVGGViT:
    """
    Load model from checkpoint.
    
    Args:
        checkpoint_path: Path to the checkpoint file
        model: Model instance to load weights into
        device: Device to load the model on
        
    Returns:
        Model with loaded weights
    """
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()
    
    print(f"Model loaded from {checkpoint_path}")
    if 'val_acc' in checkpoint:
        print(f"Checkpoint validation accuracy: {checkpoint['val_acc']:.2f}%")
    
    return model


def compare_models(models: Dict[str, nn.Module], test_loader, device: torch.device) -> Dict[str, Dict[str, float]]:
    """
    Compare multiple models on the same test set.
    
    Args:
        models: Dictionary of model_name -> model
        test_loader: DataLoader for test data
        device: Device to run evaluation on
        
    Returns:
        Dictionary of model_name -> metrics
    """
    results = {}
    
    for name, model in models.items():
        model.eval()
        correct = 0
        total = 0
        total_time = 0
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(device), target.to(device)
                
                start_time = torch.cuda.Event(enable_timing=True)
                end_time = torch.cuda.Event(enable_timing=True)
                
                start_time.record()
                output = model(data)
                end_time.record()
                
                torch.cuda.synchronize()
                batch_time = start_time.elapsed_time(end_time)
                total_time += batch_time
                
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += target.size(0)
        
        accuracy = 100. * correct / total
        avg_inference_time = total_time / len(test_loader)
        
        results[name] = {
            'accuracy': accuracy,
            'avg_inference_time_ms': avg_inference_time,
            'total_params': sum(p.numel() for p in model.parameters())
        }
    
    return results


if __name__ == "__main__":
    # Example usage
    from hybrid_vgg_vit import create_hybrid_vgg_vit
    
    # Create model
    model = create_hybrid_vgg_vit(num_classes=1000)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Analyze model
    analyzer = ModelAnalyzer(model, device)
    print(analyzer.get_model_summary())
    
    # Test inference utilities
    # inference_utils = InferenceUtils(model, device)
    # predictions = inference_utils.predict_image('path/to/image.jpg')
    # print("Predictions:", predictions)
