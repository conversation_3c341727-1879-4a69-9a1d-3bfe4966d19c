"""
Training script for Hybrid VGG-ViT model.

This script provides utilities for training the hybrid model with proper
data loading, optimization, and evaluation. Includes automatic sample
data generation if real datasets are not available.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
import torchvision.datasets as datasets
from tqdm import tqdm
import time
import os
import warnings
import numpy as np
from PIL import Image
from typing import Tuple, Dict, Any, Optional, List

from hybrid_vgg_vit import create_hybrid_vgg_vit, ConfigurationError, DimensionError


class SampleDataset(Dataset):
    """
    Sample dataset generator for testing when real data is not available.
    """

    def __init__(
        self,
        num_samples: int = 1000,
        num_classes: int = 10,
        image_size: int = 224,
        transform: Optional[transforms.Compose] = None
    ):
        self.num_samples = num_samples
        self.num_classes = num_classes
        self.image_size = image_size
        self.transform = transform

        # Generate random labels
        self.labels = torch.randint(0, num_classes, (num_samples,))

        print(f"Created sample dataset with {num_samples} samples and {num_classes} classes")

    def __len__(self) -> int:
        return self.num_samples

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int]:
        # Generate random image
        image = torch.randint(0, 256, (3, self.image_size, self.image_size), dtype=torch.uint8)
        image = transforms.ToPILImage()(image)

        if self.transform:
            image = self.transform(image)
        else:
            image = transforms.ToTensor()(image)

        return image, self.labels[idx].item()


def create_sample_data_loaders(
    num_classes: int = 10,
    train_samples: int = 1000,
    val_samples: int = 200,
    batch_size: int = 16,
    num_workers: int = 2
) -> Tuple[DataLoader, DataLoader]:
    """
    Create sample data loaders for testing.

    Args:
        num_classes: Number of classes
        train_samples: Number of training samples
        val_samples: Number of validation samples
        batch_size: Batch size
        num_workers: Number of worker processes

    Returns:
        Tuple of (train_loader, val_loader)
    """
    # Data transforms
    train_transform = transforms.Compose([
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.RandomRotation(degrees=10),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    val_transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # Create datasets
    train_dataset = SampleDataset(
        num_samples=train_samples,
        num_classes=num_classes,
        transform=train_transform
    )

    val_dataset = SampleDataset(
        num_samples=val_samples,
        num_classes=num_classes,
        transform=val_transform
    )

    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available()
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available()
    )

    return train_loader, val_loader


class ModelTrainer:
    """
    Trainer class for the Hybrid VGG-ViT model.
    """
    
    def __init__(
        self,
        model: nn.Module,
        device: torch.device,
        learning_rate: float = 1e-4,
        weight_decay: float = 1e-4
    ):
        self.model = model.to(device)
        self.device = device
        
        # Optimizer with different learning rates for VGG and Transformer parts
        vgg_params = []
        transformer_params = []
        
        for name, param in model.named_parameters():
            if 'vgg_features' in name:
                vgg_params.append(param)
            else:
                transformer_params.append(param)
        
        self.optimizer = optim.AdamW([
            {'params': vgg_params, 'lr': learning_rate * 0.1},  # Lower LR for pre-trained-like VGG
            {'params': transformer_params, 'lr': learning_rate}
        ], weight_decay=weight_decay)
        
        self.criterion = nn.CrossEntropyLoss()
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=100, eta_min=1e-6
        )
        
    def train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        pbar = tqdm(train_loader, desc="Training")
        for batch_idx, (data, target) in enumerate(pbar):
            data, target = data.to(self.device), target.to(self.device)
            
            self.optimizer.zero_grad()
            output = self.model(data)
            loss = self.criterion(output, target)
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100. * correct / total:.2f}%'
            })
        
        return {
            'loss': total_loss / len(train_loader),
            'accuracy': 100. * correct / total
        }
    
    def validate(self, val_loader: DataLoader) -> Dict[str, float]:
        """Validate the model."""
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in tqdm(val_loader, desc="Validation"):
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                loss = self.criterion(output, target)
                
                total_loss += loss.item()
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += target.size(0)
        
        return {
            'loss': total_loss / len(val_loader),
            'accuracy': 100. * correct / total
        }
    
    def train(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        epochs: int,
        save_dir: str = "checkpoints"
    ) -> Dict[str, list]:
        """Full training loop."""
        os.makedirs(save_dir, exist_ok=True)
        
        history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': []
        }
        
        best_val_acc = 0.0
        
        for epoch in range(epochs):
            print(f"\nEpoch {epoch + 1}/{epochs}")
            print("-" * 50)
            
            # Training
            train_metrics = self.train_epoch(train_loader)
            
            # Validation
            val_metrics = self.validate(val_loader)
            
            # Update learning rate
            self.scheduler.step()
            
            # Record metrics
            history['train_loss'].append(train_metrics['loss'])
            history['train_acc'].append(train_metrics['accuracy'])
            history['val_loss'].append(val_metrics['loss'])
            history['val_acc'].append(val_metrics['accuracy'])
            
            print(f"Train Loss: {train_metrics['loss']:.4f}, Train Acc: {train_metrics['accuracy']:.2f}%")
            print(f"Val Loss: {val_metrics['loss']:.4f}, Val Acc: {val_metrics['accuracy']:.2f}%")
            
            # Save best model
            if val_metrics['accuracy'] > best_val_acc:
                best_val_acc = val_metrics['accuracy']
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'val_acc': best_val_acc,
                }, os.path.join(save_dir, 'best_model.pth'))
                print(f"New best model saved with validation accuracy: {best_val_acc:.2f}%")
        
        return history


def get_data_loaders(
    data_dir: str,
    batch_size: int = 32,
    num_workers: int = 4,
    input_size: int = 224,
    fallback_to_sample: bool = True
) -> Tuple[DataLoader, DataLoader, int]:
    """
    Create data loaders for training and validation with error handling.

    Args:
        data_dir: Path to dataset directory
        batch_size: Batch size for training
        num_workers: Number of worker processes
        input_size: Input image size
        fallback_to_sample: Whether to create sample data if real data fails

    Returns:
        Tuple of (train_loader, val_loader, num_classes)

    Raises:
        FileNotFoundError: If data directory doesn't exist and fallback is disabled
    """

    # Validate input size
    if input_size != 224:
        warnings.warn(f"Model expects 224x224 images, got {input_size}. This may cause errors.")

    # Data augmentation for training
    train_transform = transforms.Compose([
        transforms.Resize((input_size + 32, input_size + 32)),
        transforms.RandomCrop(input_size),
        transforms.RandomHorizontalFlip(p=0.5),
        transforms.RandomRotation(degrees=15),
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # Validation transform (no augmentation)
    val_transform = transforms.Compose([
        transforms.Resize((input_size, input_size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    try:
        # Try to load real data
        if not os.path.exists(data_dir):
            raise FileNotFoundError(f"Data directory '{data_dir}' does not exist")

        train_dir = os.path.join(data_dir, 'train')
        val_dir = os.path.join(data_dir, 'val')

        if not os.path.exists(train_dir):
            raise FileNotFoundError(f"Training directory '{train_dir}' does not exist")
        if not os.path.exists(val_dir):
            raise FileNotFoundError(f"Validation directory '{val_dir}' does not exist")

        # Create datasets
        train_dataset = datasets.ImageFolder(
            root=train_dir,
            transform=train_transform
        )

        val_dataset = datasets.ImageFolder(
            root=val_dir,
            transform=val_transform
        )

        # Validate that datasets have samples
        if len(train_dataset) == 0:
            raise ValueError(f"No training samples found in '{train_dir}'")
        if len(val_dataset) == 0:
            raise ValueError(f"No validation samples found in '{val_dir}'")

        # Check class consistency
        if train_dataset.classes != val_dataset.classes:
            warnings.warn("Training and validation datasets have different classes")

        num_classes = len(train_dataset.classes)
        print(f"Loaded real dataset: {len(train_dataset)} train, {len(val_dataset)} val, {num_classes} classes")

    except (FileNotFoundError, ValueError, OSError) as e:
        if not fallback_to_sample:
            raise e

        print(f"Failed to load real data: {e}")
        print("Falling back to sample data generation...")

        # Create sample data
        num_classes = 10  # Default for sample data
        train_loader, val_loader = create_sample_data_loaders(
            num_classes=num_classes,
            batch_size=batch_size,
            num_workers=min(num_workers, 2)  # Reduce workers for sample data
        )

        return train_loader, val_loader, num_classes

    # Create data loaders for real data
    try:
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=torch.cuda.is_available(),
            drop_last=True  # Ensure consistent batch sizes
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=torch.cuda.is_available()
        )

    except Exception as e:
        if not fallback_to_sample:
            raise RuntimeError(f"Failed to create data loaders: {e}")

        print(f"Failed to create data loaders: {e}")
        print("Falling back to sample data...")

        num_classes = 10
        train_loader, val_loader = create_sample_data_loaders(
            num_classes=num_classes,
            batch_size=batch_size,
            num_workers=min(num_workers, 2)
        )

    return train_loader, val_loader, num_classes


def validate_training_config(config: Dict[str, Any]) -> None:
    """
    Validate training configuration.

    Args:
        config: Training configuration dictionary

    Raises:
        ConfigurationError: If configuration is invalid
    """
    required_keys = ['num_classes', 'batch_size', 'learning_rate', 'epochs', 'embed_dim']
    for key in required_keys:
        if key not in config:
            raise ConfigurationError(f"Missing required config key: {key}")

    if config['num_classes'] <= 0:
        raise ConfigurationError(f"num_classes must be positive, got {config['num_classes']}")

    if config['batch_size'] <= 0:
        raise ConfigurationError(f"batch_size must be positive, got {config['batch_size']}")

    if config['learning_rate'] <= 0:
        raise ConfigurationError(f"learning_rate must be positive, got {config['learning_rate']}")

    if config['epochs'] <= 0:
        raise ConfigurationError(f"epochs must be positive, got {config['epochs']}")

    # Warn about potentially problematic settings
    if config['batch_size'] > 64:
        warnings.warn(f"Large batch size ({config['batch_size']}) may cause memory issues")

    if config['learning_rate'] > 1e-2:
        warnings.warn(f"High learning rate ({config['learning_rate']}) may cause training instability")


def main():
    """Main training function with comprehensive error handling."""
    # Configuration
    config = {
        'num_classes': 10,    # Will be updated based on actual dataset
        'batch_size': 16,     # Adjust based on GPU memory
        'learning_rate': 1e-4,
        'epochs': 10,         # Reduced for demo
        'input_size': 224,
        'embed_dim': 768,
        'num_heads': 12,
        'num_transformer_layers': 6,
        'data_dir': 'data/imagenet',  # Update with your data path
        'save_dir': 'checkpoints',
        'use_sample_data': False  # Set to True to force sample data
    }

    try:
        # Validate configuration
        validate_training_config(config)

        # Device setup
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {device}")

        if device.type == 'cuda':
            print(f"GPU: {torch.cuda.get_device_name()}")
            print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

        # Create data loaders with error handling
        print("Setting up data loaders...")
        try:
            if config['use_sample_data']:
                train_loader, val_loader, num_classes = create_sample_data_loaders(
                    num_classes=config['num_classes'],
                    batch_size=config['batch_size']
                ), create_sample_data_loaders(
                    num_classes=config['num_classes'],
                    batch_size=config['batch_size']
                )[1], config['num_classes']
                train_loader, val_loader = train_loader[0], train_loader[1]
            else:
                train_loader, val_loader, num_classes = get_data_loaders(
                    data_dir=config['data_dir'],
                    batch_size=config['batch_size'],
                    input_size=config['input_size'],
                    fallback_to_sample=True
                )

            # Update config with actual number of classes
            config['num_classes'] = num_classes
            print(f"Dataset: {len(train_loader)} train batches, {len(val_loader)} val batches, {num_classes} classes")

        except Exception as e:
            print(f"Critical error setting up data: {e}")
            print("Creating minimal sample dataset for demonstration...")
            train_loader, val_loader = create_sample_data_loaders(
                num_classes=5,
                train_samples=100,
                val_samples=20,
                batch_size=config['batch_size']
            )
            config['num_classes'] = 5

        # Create model with validation
        print("Creating model...")
        try:
            model = create_hybrid_vgg_vit(
                num_classes=config['num_classes'],
                embed_dim=config['embed_dim'],
                num_heads=config['num_heads'],
                num_transformer_layers=config['num_transformer_layers'],
                validate_config=True
            )

            param_count = sum(p.numel() for p in model.parameters())
            print(f"Model created successfully with {param_count:,} parameters")

        except (ConfigurationError, DimensionError) as e:
            print(f"Model configuration error: {e}")
            print("Trying with smaller, safer configuration...")

            model = create_hybrid_vgg_vit(
                num_classes=config['num_classes'],
                embed_dim=384,
                num_heads=6,
                num_transformer_layers=4,
                validate_config=True
            )
            print("Created model with reduced configuration")

        # Test model with sample input
        print("Testing model forward pass...")
        try:
            sample_input = torch.randn(2, 3, 224, 224).to(device)
            model.to(device)
            with torch.no_grad():
                output = model(sample_input)
            print(f"Model test successful: input {sample_input.shape} -> output {output.shape}")
        except Exception as e:
            print(f"Model forward pass failed: {e}")
            return

        # Create trainer
        print("Setting up trainer...")
        trainer = ModelTrainer(
            model=model,
            device=device,
            learning_rate=config['learning_rate']
        )

        # Start training
        print("Starting training...")
        print(f"Configuration: {config['epochs']} epochs, batch size {config['batch_size']}")

        history = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=config['epochs'],
            save_dir=config['save_dir']
        )

        print("Training completed successfully!")
        print(f"Final validation accuracy: {history['val_acc'][-1]:.2f}%")

    except KeyboardInterrupt:
        print("\nTraining interrupted by user")
    except Exception as e:
        print(f"Unexpected error during training: {e}")
        import traceback
        traceback.print_exc()
        print("\nTroubleshooting tips:")
        print("1. Reduce batch_size if you get CUDA out of memory errors")
        print("2. Use embed_dim=384, num_heads=6 for smaller models")
        print("3. Set use_sample_data=True to test with generated data")
        print("4. Check that your data directory structure is correct")


if __name__ == "__main__":
    main()
