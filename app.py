"""
Hugging Face Spaces App for VGG-inspired Vision Transformers
Interactive web interface for model inference and visualization
"""

import gradio as gr
import torch
import torch.nn.functional as F
import numpy as np
import json
import time
import io
import base64
from PIL import Image
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Optional
import pandas as pd
from datetime import datetime
import cv2

from hybrid_vgg_vit import (
    create_hybrid_vgg_vit, 
    create_model_from_config,
    get_recommended_configs
)
from model_utils import ModelAnalyzer, InferenceUtils

# Global variables for model management
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
MODELS = {}
CURRENT_MODEL = None
CURRENT_CONFIG = None

# ImageNet class names (subset for demo)
with open('imagenet_classes.json', 'r') as f:
    IMAGENET_CLASSES = json.load(f)

def load_model(config_name: str, checkpoint_path: Optional[str] = None):
    """Load or create model based on configuration."""
    global CURRENT_MODEL, CURRENT_CONFIG, MODELS
    
    if config_name in MODELS and checkpoint_path is None:
        CURRENT_MODEL = MODELS[config_name]
        CURRENT_CONFIG = config_name
        return f"Model '{config_name}' loaded from cache"
    
    try:
        # Create model from config
        model = create_model_from_config(config_name, num_classes=1000)
        
        # Load checkpoint if provided
        if checkpoint_path:
            checkpoint = torch.load(checkpoint_path, map_location=DEVICE)
            model.load_state_dict(checkpoint['model_state_dict'])
        
        model = model.to(DEVICE)
        model.eval()
        
        # Cache the model
        MODELS[config_name] = model
        CURRENT_MODEL = model
        CURRENT_CONFIG = config_name
        
        return f"Model '{config_name}' loaded successfully"
    except Exception as e:
        return f"Error loading model: {str(e)}"

def get_model_info():
    """Get current model information."""
    if CURRENT_MODEL is None:
        return "No model loaded"
    
    analyzer = ModelAnalyzer(CURRENT_MODEL, DEVICE)
    complexity = analyzer.analyze_model_complexity()
    
    info = f"""
**Current Model: {CURRENT_CONFIG}**

**Architecture:**
- VGG Blocks: 4 (64→128→256→512 channels)
- Transformer Layers: {len(CURRENT_MODEL.transformer_blocks)}
- Embedding Dimension: {CURRENT_MODEL.norm.normalized_shape[0]}
- Total Parameters: {complexity['parameters']['total']:,}

**Model Size:** {complexity['model_size_mb']:.1f} MB

**Performance Characteristics:**
- Input Size: 224×224×3
- Output Classes: 1000
- Device: {DEVICE}
"""
    return info

def preprocess_image(image: Image.Image, size: Tuple[int, int] = (224, 224)):
    """Preprocess image for model input."""
    # Convert to RGB if necessary
    if image.mode != 'RGB':
        image = image.convert('RGB')
    
    # Resize
    image = image.resize(size, Image.Resampling.LANCZOS)
    
    # Convert to tensor and normalize
    img_array = np.array(image).astype(np.float32) / 255.0
    img_tensor = torch.from_numpy(img_array).permute(2, 0, 1)
    
    # ImageNet normalization
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    img_tensor = (img_tensor - mean) / std
    
    return img_tensor.unsqueeze(0)

def predict_single_image(
    image: Image.Image,
    confidence_threshold: float = 0.01,
    top_k: int = 5
) -> Tuple[Dict, float, Image.Image, Image.Image]:
    """Predict classes for a single image."""
    if CURRENT_MODEL is None:
        return {"error": "No model loaded"}, 0.0, None, None
    
    start_time = time.time()
    
    # Preprocess image
    input_tensor = preprocess_image(image).to(DEVICE)
    
    # Make prediction
    with torch.no_grad():
        # Get predictions
        outputs = CURRENT_MODEL(input_tensor)
        probabilities = F.softmax(outputs, dim=1)
        
        # Get feature maps for visualization
        features = CURRENT_MODEL.get_feature_maps(input_tensor)
    
    inference_time = (time.time() - start_time) * 1000  # Convert to ms
    
    # Get top predictions
    top_probs, top_indices = torch.topk(probabilities[0], min(top_k, 1000))
    
    # Format results
    results = []
    for prob, idx in zip(top_probs, top_indices):
        prob_value = prob.item()
        if prob_value >= confidence_threshold:
            class_name = IMAGENET_CLASSES.get(str(idx.item()), f"Class_{idx.item()}")
            results.append({
                "class": class_name,
                "confidence": prob_value,
                "index": idx.item()
            })
    
    # Create visualizations
    attention_map = visualize_attention_map(features)
    feature_viz = visualize_features(features)
    
    return {
        "predictions": results,
        "inference_time_ms": inference_time,
        "model": CURRENT_CONFIG,
        "device": str(DEVICE)
    }, inference_time, attention_map, feature_viz

def visualize_attention_map(features: torch.Tensor) -> Image.Image:
    """Create attention/activation heatmap from features."""
    # Average across channels to get spatial attention
    attention = features.mean(dim=1)[0].cpu().numpy()
    
    # Normalize to 0-1
    attention = (attention - attention.min()) / (attention.max() - attention.min())
    
    # Create heatmap
    plt.figure(figsize=(6, 6))
    plt.imshow(attention, cmap='hot', interpolation='bilinear')
    plt.colorbar(label='Activation Strength')
    plt.title('Spatial Attention Map')
    plt.axis('off')
    
    # Convert to image
    buf = io.BytesIO()
    plt.savefig(buf, format='png', bbox_inches='tight', dpi=150)
    buf.seek(0)
    plt.close()
    
    return Image.open(buf)

def visualize_features(features: torch.Tensor, num_features: int = 16) -> Image.Image:
    """Visualize top feature maps."""
    feature_maps = features[0].cpu().numpy()
    
    # Select top features by activation strength
    activation_strengths = feature_maps.mean(axis=(1, 2))
    top_indices = np.argsort(activation_strengths)[-num_features:][::-1]
    
    # Create grid visualization
    grid_size = int(np.ceil(np.sqrt(num_features)))
    fig, axes = plt.subplots(grid_size, grid_size, figsize=(10, 10))
    axes = axes.flatten()
    
    for i, idx in enumerate(top_indices):
        if i < len(axes):
            feature_map = feature_maps[idx]
            # Normalize
            feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min())
            axes[i].imshow(feature_map, cmap='viridis')
            axes[i].set_title(f'Feature {idx}')
            axes[i].axis('off')
    
    # Hide unused subplots
    for i in range(len(top_indices), len(axes)):
        axes[i].axis('off')
    
    plt.suptitle('Top Feature Maps by Activation Strength', fontsize=14)
    plt.tight_layout()
    
    # Convert to image
    buf = io.BytesIO()
    plt.savefig(buf, format='png', bbox_inches='tight', dpi=150)
    buf.seek(0)
    plt.close()
    
    return Image.open(buf)

def batch_predict(
    images: List[Image.Image],
    confidence_threshold: float = 0.01,
    top_k: int = 5
) -> pd.DataFrame:
    """Process multiple images and return results as DataFrame."""
    if CURRENT_MODEL is None:
        return pd.DataFrame({"error": ["No model loaded"]})
    
    results = []
    
    for idx, image in enumerate(images):
        result, inference_time, _, _ = predict_single_image(image, confidence_threshold, top_k)
        
        if "predictions" in result:
            for pred in result["predictions"]:
                results.append({
                    "image_index": idx + 1,
                    "class": pred["class"],
                    "confidence": pred["confidence"],
                    "inference_time_ms": inference_time
                })
    
    return pd.DataFrame(results)

def export_results(results: Dict, format: str = "json") -> str:
    """Export results in specified format."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if format == "json":
        filename = f"predictions_{timestamp}.json"
        content = json.dumps(results, indent=2)
    else:  # CSV
        filename = f"predictions_{timestamp}.csv"
        if isinstance(results, pd.DataFrame):
            content = results.to_csv(index=False)
        else:
            # Convert dict to CSV format
            df = pd.DataFrame(results.get("predictions", []))
            content = df.to_csv(index=False)
    
    return content, filename

def create_performance_plot(model_configs: List[str]) -> Image.Image:
    """Create performance comparison plot for different model configurations."""
    configs = get_recommended_configs()
    
    # Extract data for plotting
    data = []
    for config_name in model_configs:
        if config_name in configs:
            config = configs[config_name]
            # Estimate parameters based on config
            params = estimate_parameters(config)
            data.append({
                "Model": config_name.capitalize(),
                "Parameters (M)": params / 1e6,
                "Embedding Dim": config["embed_dim"],
                "Transformer Layers": config["num_transformer_layers"]
            })
    
    df = pd.DataFrame(data)
    
    # Create subplots
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Parameters comparison
    axes[0].bar(df["Model"], df["Parameters (M)"], color='skyblue')
    axes[0].set_ylabel("Parameters (Millions)")
    axes[0].set_title("Model Size Comparison")
    axes[0].tick_params(axis='x', rotation=45)
    
    # Embedding dimension
    axes[1].bar(df["Model"], df["Embedding Dim"], color='lightgreen')
    axes[1].set_ylabel("Embedding Dimension")
    axes[1].set_title("Embedding Dimension by Model")
    axes[1].tick_params(axis='x', rotation=45)
    
    # Transformer layers
    axes[2].bar(df["Model"], df["Transformer Layers"], color='salmon')
    axes[2].set_ylabel("Number of Layers")
    axes[2].set_title("Transformer Layers by Model")
    axes[2].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    
    # Convert to image
    buf = io.BytesIO()
    plt.savefig(buf, format='png', bbox_inches='tight', dpi=150)
    buf.seek(0)
    plt.close()
    
    return Image.open(buf)

def estimate_parameters(config: Dict) -> int:
    """Estimate number of parameters based on configuration."""
    # Simplified estimation
    embed_dim = config["embed_dim"]
    num_layers = config["num_transformer_layers"]
    
    # VGG parameters (approximately)
    vgg_params = 15_000_000  # ~15M for VGG blocks
    
    # Transformer parameters (approximately)
    transformer_params = num_layers * (
        4 * embed_dim * embed_dim +  # Attention
        2 * embed_dim * embed_dim * 4  # MLP
    )
    
    # Classification head
    head_params = embed_dim * 1000
    
    return vgg_params + transformer_params + head_params

# Create Gradio interface
def create_interface():
    """Create the Gradio interface."""
    
    with gr.Blocks(title="VGG-ViT Vision Transformer", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🤖 VGG-inspired Vision Transformers
        
        Interactive interface for hybrid VGG-ViT models combining convolutional feature extraction with transformer-based spatial modeling.
        
        **Features:**
        - 🎯 Multiple model variants (Tiny, Small, Base, Large)
        - 📸 Single and batch image processing
        - 📊 Attention map and feature visualizations
        - ⚡ Real-time performance metrics
        - 💾 Export results in JSON/CSV format
        """)
        
        with gr.Tabs():
            # Model Selection Tab
            with gr.TabItem("🔧 Model Configuration"):
                with gr.Row():
                    with gr.Column(scale=1):
                        model_selector = gr.Dropdown(
                            choices=["tiny", "small", "base", "large"],
                            value="small",
                            label="Select Model Variant",
                            info="Choose model size based on your requirements"
                        )
                        
                        load_btn = gr.Button("Load Model", variant="primary")
                        model_status = gr.Textbox(label="Status", interactive=False)
                        
                        gr.Markdown("### Model Variants")
                        configs = get_recommended_configs()
                        for name, config in configs.items():
                            gr.Markdown(f"**{name.capitalize()}**: {config['description']}")
                    
                    with gr.Column(scale=2):
                        model_info = gr.Markdown(get_model_info())
                        performance_plot = gr.Image(label="Model Comparison")
                
                def update_model(model_name):
                    status = load_model(model_name)
                    info = get_model_info()
                    plot = create_performance_plot(list(configs.keys()))
                    return status, info, plot
                
                load_btn.click(
                    update_model,
                    inputs=[model_selector],
                    outputs=[model_status, model_info, performance_plot]
                )
            
            # Single Image Prediction Tab
            with gr.TabItem("🖼️ Single Image"):
                with gr.Row():
                    with gr.Column():
                        input_image = gr.Image(
                            label="Upload Image",
                            type="pil",
                            height=300
                        )
                        
                        with gr.Row():
                            confidence_threshold = gr.Slider(
                                minimum=0.0,
                                maximum=1.0,
                                value=0.01,
                                step=0.01,
                                label="Confidence Threshold"
                            )
                            top_k = gr.Slider(
                                minimum=1,
                                maximum=10,
                                value=5,
                                step=1,
                                label="Top-K Predictions"
                            )
                        
                        predict_btn = gr.Button("🔍 Analyze Image", variant="primary")
                    
                    with gr.Column():
                        predictions_json = gr.JSON(label="Predictions")
                        inference_time = gr.Number(label="Inference Time (ms)", precision=2)
                
                with gr.Row():
                    attention_map = gr.Image(label="Attention Map", height=300)
                    feature_viz = gr.Image(label="Feature Visualizations", height=300)
                
                predict_btn.click(
                    predict_single_image,
                    inputs=[input_image, confidence_threshold, top_k],
                    outputs=[predictions_json, inference_time, attention_map, feature_viz]
                )
            
            # Batch Processing Tab
            with gr.TabItem("📁 Batch Processing"):
                with gr.Row():
                    with gr.Column():
                        batch_images = gr.File(
                            label="Upload Multiple Images",
                            file_count="multiple",
                            file_types=["image"]
                        )
                        
                        batch_confidence = gr.Slider(
                            minimum=0.0,
                            maximum=1.0,
                            value=0.01,
                            step=0.01,
                            label="Confidence Threshold"
                        )
                        
                        batch_top_k = gr.Slider(
                            minimum=1,
                            maximum=10,
                            value=5,
                            step=1,
                            label="Top-K Predictions"
                        )
                        
                        batch_predict_btn = gr.Button("🚀 Process Batch", variant="primary")
                    
                    with gr.Column():
                        batch_results = gr.Dataframe(
                            label="Batch Results",
                            headers=["image_index", "class", "confidence", "inference_time_ms"]
                        )
                        
                        with gr.Row():
                            export_format = gr.Radio(
                                choices=["json", "csv"],
                                value="csv",
                                label="Export Format"
                            )
                            export_btn = gr.Button("💾 Export Results")
                        
                        export_file = gr.File(label="Download Results")
                
                def process_batch(files, confidence, k):
                    if not files:
                        return pd.DataFrame()
                    
                    images = []
                    for file in files:
                        img = Image.open(file.name)
                        images.append(img)
                    
                    return batch_predict(images, confidence, k)
                
                batch_predict_btn.click(
                    process_batch,
                    inputs=[batch_images, batch_confidence, batch_top_k],
                    outputs=[batch_results]
                )
                
                def export_batch_results(results_df, format):
                    if results_df is None or results_df.empty:
                        return None
                    
                    content, filename = export_results(results_df, format)
                    
                    # Save to temporary file
                    temp_path = f"/tmp/{filename}"
                    with open(temp_path, 'w') as f:
                        f.write(content)
                    
                    return temp_path
                
                export_btn.click(
                    export_batch_results,
                    inputs=[batch_results, export_format],
                    outputs=[export_file]
                )
            
            # Example Gallery Tab
            with gr.TabItem("🎨 Example Gallery"):
                gr.Markdown("""
                ### Pre-loaded Examples
                Try these example images to see the model in action!
                """)
                
                example_images = [
                    ["examples/cat.jpg", 0.01, 5],
                    ["examples/dog.jpg", 0.01, 5],
                    ["examples/car.jpg", 0.01, 5],
                    ["examples/airplane.jpg", 0.01, 5],
                    ["examples/flower.jpg", 0.01, 5]
                ]
                
                gr.Examples(
                    examples=example_images,
                    inputs=[input_image, confidence_threshold, top_k],
                    outputs=[predictions_json, inference_time, attention_map, feature_viz],
                    fn=predict_single_image,
                    cache_examples=True
                )
            
            # API Documentation Tab
            with gr.TabItem("📚 API Documentation"):
                gr.Markdown("""
                ## API Endpoint Documentation
                
                This Hugging Face Space provides a REST API for programmatic access.
                
                ### Endpoint
                ```
                POST https://huggingface.co/spaces/{your-username}/vgg-vit-transformer/api/predict
                ```
                
                ### Request Format
                ```json
                {
                    "image": "base64_encoded_image_string",
                    "model": "small",  // Options: tiny, small, base, large
                    "confidence_threshold": 0.01,
                    "top_k": 5
                }
                ```
                
                ### Response Format
                ```json
                {
                    "predictions": [
                        {
                            "class": "tabby cat",
                            "confidence": 0.95,
                            "index": 281
                        }
                    ],
                    "inference_time_ms": 45.2,
                    "model": "small",
                    "device": "cuda"
                }
                ```
                
                ### Python Example
                ```python
                import requests
                import base64
                from PIL import Image
                import io
                
                # Load and encode image
                with open("image.jpg", "rb") as f:
                    image_bytes = f.read()
                image_base64 = base64.b64encode(image_bytes).decode()
                
                # Make request
                response = requests.post(
                    "https://huggingface.co/spaces/{your-username}/vgg-vit-transformer/api/predict",
                    json={
                        "image": image_base64,
                        "model": "small",
                        "confidence_threshold": 0.01,
                        "top_k": 5
                    }
                )
                
                # Get results
                results = response.json()
                print(results["predictions"])
                ```
                
                ### Rate Limits
                - 100 requests per minute per IP
                - Maximum image size: 10MB
                - Supported formats: JPEG, PNG, WebP
                """)
        
        # Footer
        gr.Markdown("""
        ---
        
        **Note:** This is a demonstration interface for VGG-inspired Vision Transformers. 
        Models are initialized with random weights unless a checkpoint is loaded.
        
        Built with ❤️ using Gradio and PyTorch
        """)
    
    return demo

# Initialize default model on startup
load_model("small")

# Create and launch the interface
if __name__ == "__main__":
    demo = create_interface()
    demo.launch(
        share=True,
        server_name="0.0.0.0",
        server_port=7860
    )