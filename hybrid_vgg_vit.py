"""
Hybrid VGG-Vision Transformer Model

This module implements a hybrid deep learning architecture that combines:
1. VGG-style convolutional blocks for feature extraction
2. Vision Transformer blocks for spatial relationship modeling
3. Classification head for final predictions

Author: AI Assistant
Framework: PyTorch

Technical Specifications:
- Input: 224x224x3 images
- VGG Blocks: 4 blocks with channels [64, 128, 256, 512]
- Transformer: 6 layers with 768 embedding dimension and 12 attention heads
- Output: 1000 classes (ImageNet-style)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import warnings
from typing import Tuple, Optional, List, Dict, Any


class DimensionError(Exception):
    """Custom exception for dimension compatibility issues."""
    pass


class ConfigurationError(Exception):
    """Custom exception for invalid model configuration."""
    pass


def validate_model_config(
    embed_dim: int,
    num_heads: int,
    num_transformer_layers: int,
    num_classes: int,
    input_channels: int = 3
) -> None:
    """
    Validate model configuration parameters.

    Args:
        embed_dim: Transformer embedding dimension
        num_heads: Number of attention heads
        num_transformer_layers: Number of transformer blocks
        num_classes: Number of output classes
        input_channels: Number of input channels

    Raises:
        ConfigurationError: If configuration is invalid
    """
    if embed_dim <= 0:
        raise ConfigurationError(f"embed_dim must be positive, got {embed_dim}")

    if embed_dim % num_heads != 0:
        raise ConfigurationError(
            f"embed_dim ({embed_dim}) must be divisible by num_heads ({num_heads}). "
            f"Try embed_dim=768 with num_heads=12, or embed_dim=384 with num_heads=6."
        )

    if num_heads <= 0:
        raise ConfigurationError(f"num_heads must be positive, got {num_heads}")

    if num_transformer_layers <= 0:
        raise ConfigurationError(f"num_transformer_layers must be positive, got {num_transformer_layers}")

    if num_classes <= 0:
        raise ConfigurationError(f"num_classes must be positive, got {num_classes}")

    if input_channels <= 0:
        raise ConfigurationError(f"input_channels must be positive, got {input_channels}")

    # Warn about potentially problematic configurations
    if embed_dim > 2048:
        warnings.warn(f"Large embed_dim ({embed_dim}) may cause memory issues")

    if num_transformer_layers > 24:
        warnings.warn(f"Many transformer layers ({num_transformer_layers}) may cause training instability")


def debug_tensor_shapes(tensor: torch.Tensor, name: str, expected_shape: Optional[Tuple] = None) -> None:
    """
    Debug utility to print tensor shapes and validate expected dimensions.

    Args:
        tensor: Input tensor
        name: Name for debugging output
        expected_shape: Expected shape (optional)
    """
    actual_shape = tuple(tensor.shape)
    print(f"DEBUG: {name} shape: {actual_shape}")

    if expected_shape is not None:
        if len(actual_shape) != len(expected_shape):
            raise DimensionError(
                f"{name}: Expected {len(expected_shape)} dimensions, got {len(actual_shape)}"
            )

        for i, (actual, expected) in enumerate(zip(actual_shape, expected_shape)):
            if expected != -1 and actual != expected:  # -1 means any size is acceptable
                raise DimensionError(
                    f"{name}: Dimension {i} mismatch. Expected {expected}, got {actual}"
                )


class VGGBlock(nn.Module):
    """
    VGG-style convolutional block with 3x3 convolutions, ReLU activation, and max pooling.

    Args:
        in_channels: Number of input channels
        out_channels: Number of output channels
        num_convs: Number of convolutional layers in the block
    """

    def __init__(self, in_channels: int, out_channels: int, num_convs: int = 2):
        super(VGGBlock, self).__init__()

        # Validate inputs
        if in_channels <= 0 or out_channels <= 0:
            raise ConfigurationError(f"Channel counts must be positive: in={in_channels}, out={out_channels}")
        if num_convs <= 0:
            raise ConfigurationError(f"num_convs must be positive, got {num_convs}")

        self.in_channels = in_channels
        self.out_channels = out_channels
        self.num_convs = num_convs

        layers = []
        for i in range(num_convs):
            layers.extend([
                nn.Conv2d(
                    in_channels if i == 0 else out_channels,
                    out_channels,
                    kernel_size=3,
                    padding=1,
                    bias=False
                ),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ])

        layers.append(nn.MaxPool2d(kernel_size=2, stride=2))
        self.block = nn.Sequential(*layers)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through VGG block.

        Args:
            x: Input tensor of shape (B, in_channels, H, W)

        Returns:
            Output tensor of shape (B, out_channels, H//2, W//2)
        """
        if x.dim() != 4:
            raise DimensionError(f"Expected 4D input (B, C, H, W), got {x.dim()}D tensor")

        if x.size(1) != self.in_channels:
            raise DimensionError(
                f"Input channels mismatch: expected {self.in_channels}, got {x.size(1)}"
            )

        return self.block(x)


class VGGFeatureExtractor(nn.Module):
    """
    VGG-style feature extractor with 4 convolutional blocks.

    Architecture:
    - Block 1: 3 -> 64 channels, 224x224 -> 112x112
    - Block 2: 64 -> 128 channels, 112x112 -> 56x56
    - Block 3: 128 -> 256 channels, 56x56 -> 28x28
    - Block 4: 256 -> 512 channels, 28x28 -> 14x14

    Args:
        input_channels: Number of input channels (default: 3 for RGB)
    """

    def __init__(self, input_channels: int = 3):
        super(VGGFeatureExtractor, self).__init__()

        if input_channels <= 0:
            raise ConfigurationError(f"input_channels must be positive, got {input_channels}")

        self.input_channels = input_channels
        self.output_channels = 512
        self.output_spatial_size = 14  # For 224x224 input

        # VGG blocks with increasing channel dimensions
        self.block1 = VGGBlock(input_channels, 64, num_convs=2)    # 224x224 -> 112x112
        self.block2 = VGGBlock(64, 128, num_convs=2)              # 112x112 -> 56x56
        self.block3 = VGGBlock(128, 256, num_convs=3)             # 56x56 -> 28x28
        self.block4 = VGGBlock(256, 512, num_convs=3)             # 28x28 -> 14x14

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through VGG feature extractor.

        Args:
            x: Input tensor of shape (B, input_channels, 224, 224)

        Returns:
            Feature tensor of shape (B, 512, 14, 14)
        """
        if x.dim() != 4:
            raise DimensionError(f"Expected 4D input (B, C, H, W), got {x.dim()}D tensor")

        batch_size, channels, height, width = x.shape

        if channels != self.input_channels:
            raise DimensionError(
                f"Input channels mismatch: expected {self.input_channels}, got {channels}"
            )

        if height != 224 or width != 224:
            raise DimensionError(
                f"Expected input size 224x224, got {height}x{width}. "
                f"Please resize your input images to 224x224."
            )

        # Forward through blocks with dimension tracking
        x = self.block1(x)  # (B, 64, 112, 112)
        x = self.block2(x)  # (B, 128, 56, 56)
        x = self.block3(x)  # (B, 256, 28, 28)
        x = self.block4(x)  # (B, 512, 14, 14)

        # Validate output dimensions
        expected_shape = (batch_size, 512, 14, 14)
        if x.shape != expected_shape:
            raise DimensionError(
                f"VGG output shape mismatch: expected {expected_shape}, got {x.shape}"
            )

        return x


class PatchEmbedding(nn.Module):
    """
    Converts CNN feature maps to patch embeddings for Vision Transformer.

    Takes 14x14x512 feature maps from VGG and converts them to patch embeddings
    suitable for transformer processing.

    Args:
        feature_dim: Number of input feature channels (default: 512)
        embed_dim: Transformer embedding dimension (default: 768)
        patch_size: Size of each patch (default: 2)
        input_spatial_size: Spatial size of input features (default: 14)
    """

    def __init__(
        self,
        feature_dim: int = 512,
        embed_dim: int = 768,
        patch_size: int = 2,
        input_spatial_size: int = 14
    ):
        super(PatchEmbedding, self).__init__()

        # Validate inputs
        if feature_dim <= 0:
            raise ConfigurationError(f"feature_dim must be positive, got {feature_dim}")
        if embed_dim <= 0:
            raise ConfigurationError(f"embed_dim must be positive, got {embed_dim}")
        if patch_size <= 0:
            raise ConfigurationError(f"patch_size must be positive, got {patch_size}")
        if input_spatial_size % patch_size != 0:
            raise ConfigurationError(
                f"input_spatial_size ({input_spatial_size}) must be divisible by "
                f"patch_size ({patch_size})"
            )

        self.feature_dim = feature_dim
        self.embed_dim = embed_dim
        self.patch_size = patch_size
        self.input_spatial_size = input_spatial_size

        # Calculate number of patches
        self.num_patches = (input_spatial_size // patch_size) ** 2

        # Project patches to embedding dimension
        self.projection = nn.Conv2d(
            feature_dim,
            embed_dim,
            kernel_size=patch_size,
            stride=patch_size
        )

        # Learnable position embeddings (+1 for CLS token)
        self.position_embeddings = nn.Parameter(
            torch.randn(1, self.num_patches + 1, embed_dim)
        )

        # CLS token
        self.cls_token = nn.Parameter(torch.randn(1, 1, embed_dim))

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through patch embedding.

        Args:
            x: Input feature tensor of shape (B, feature_dim, H, W)

        Returns:
            Patch embeddings of shape (B, num_patches + 1, embed_dim)
        """
        if x.dim() != 4:
            raise DimensionError(f"Expected 4D input (B, C, H, W), got {x.dim()}D tensor")

        batch_size, channels, height, width = x.shape

        # Validate input dimensions
        if channels != self.feature_dim:
            raise DimensionError(
                f"Feature dimension mismatch: expected {self.feature_dim}, got {channels}"
            )

        if height != self.input_spatial_size or width != self.input_spatial_size:
            raise DimensionError(
                f"Spatial size mismatch: expected {self.input_spatial_size}x{self.input_spatial_size}, "
                f"got {height}x{width}"
            )

        # Convert to patches: (B, C, H, W) -> (B, embed_dim, H//patch_size, W//patch_size)
        x = self.projection(x)

        # Validate projection output
        expected_spatial = self.input_spatial_size // self.patch_size
        if x.shape[2] != expected_spatial or x.shape[3] != expected_spatial:
            raise DimensionError(
                f"Projection output spatial mismatch: expected {expected_spatial}x{expected_spatial}, "
                f"got {x.shape[2]}x{x.shape[3]}"
            )

        # Flatten patches: (B, embed_dim, H', W') -> (B, embed_dim, H'*W')
        x = x.flatten(2)

        # Transpose: (B, embed_dim, num_patches) -> (B, num_patches, embed_dim)
        x = x.transpose(1, 2)

        # Validate number of patches
        if x.shape[1] != self.num_patches:
            raise DimensionError(
                f"Number of patches mismatch: expected {self.num_patches}, got {x.shape[1]}"
            )

        # Add CLS token
        cls_tokens = self.cls_token.expand(batch_size, -1, -1)
        x = torch.cat([cls_tokens, x], dim=1)

        # Add position embeddings
        if x.shape[1] != self.position_embeddings.shape[1]:
            raise DimensionError(
                f"Position embedding size mismatch: expected {self.position_embeddings.shape[1]}, "
                f"got {x.shape[1]}"
            )

        x = x + self.position_embeddings

        return x


class MultiHeadSelfAttention(nn.Module):
    """
    Multi-head self-attention mechanism for Vision Transformer.

    Args:
        embed_dim: Embedding dimension (must be divisible by num_heads)
        num_heads: Number of attention heads
        dropout: Dropout probability
    """

    def __init__(self, embed_dim: int = 768, num_heads: int = 12, dropout: float = 0.1):
        super(MultiHeadSelfAttention, self).__init__()

        # Validate configuration
        if embed_dim <= 0:
            raise ConfigurationError(f"embed_dim must be positive, got {embed_dim}")
        if num_heads <= 0:
            raise ConfigurationError(f"num_heads must be positive, got {num_heads}")
        if embed_dim % num_heads != 0:
            raise ConfigurationError(
                f"embed_dim ({embed_dim}) must be divisible by num_heads ({num_heads}). "
                f"head_dim would be {embed_dim / num_heads}"
            )
        if not 0 <= dropout <= 1:
            raise ConfigurationError(f"dropout must be between 0 and 1, got {dropout}")

        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.scale = self.head_dim ** -0.5

        self.qkv = nn.Linear(embed_dim, embed_dim * 3, bias=False)
        self.proj = nn.Linear(embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through multi-head self-attention.

        Args:
            x: Input tensor of shape (B, seq_len, embed_dim)

        Returns:
            Output tensor of shape (B, seq_len, embed_dim)
        """
        if x.dim() != 3:
            raise DimensionError(f"Expected 3D input (B, seq_len, embed_dim), got {x.dim()}D tensor")

        batch_size, seq_len, embed_dim = x.shape

        if embed_dim != self.embed_dim:
            raise DimensionError(
                f"Embedding dimension mismatch: expected {self.embed_dim}, got {embed_dim}"
            )

        # Generate Q, K, V
        qkv = self.qkv(x).reshape(batch_size, seq_len, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # (3, B, num_heads, seq_len, head_dim)
        q, k, v = qkv[0], qkv[1], qkv[2]

        # Validate QKV shapes
        expected_qkv_shape = (batch_size, self.num_heads, seq_len, self.head_dim)
        for name, tensor in [("Q", q), ("K", k), ("V", v)]:
            if tensor.shape != expected_qkv_shape:
                raise DimensionError(
                    f"{name} tensor shape mismatch: expected {expected_qkv_shape}, got {tensor.shape}"
                )

        # Attention computation
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = F.softmax(attn, dim=-1)
        attn = self.dropout(attn)

        # Apply attention to values
        x = (attn @ v).transpose(1, 2).reshape(batch_size, seq_len, embed_dim)
        x = self.proj(x)

        # Validate output shape
        expected_output_shape = (batch_size, seq_len, self.embed_dim)
        if x.shape != expected_output_shape:
            raise DimensionError(
                f"Output shape mismatch: expected {expected_output_shape}, got {x.shape}"
            )

        return x


class TransformerBlock(nn.Module):
    """
    Vision Transformer block with multi-head attention and MLP.
    """
    
    def __init__(self, embed_dim: int = 768, num_heads: int = 12, mlp_ratio: float = 4.0, dropout: float = 0.1):
        super(TransformerBlock, self).__init__()
        
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = MultiHeadSelfAttention(embed_dim, num_heads, dropout)
        
        self.norm2 = nn.LayerNorm(embed_dim)
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(mlp_hidden_dim, embed_dim),
            nn.Dropout(dropout)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Multi-head self-attention with residual connection
        x = x + self.attn(self.norm1(x))
        
        # MLP with residual connection
        x = x + self.mlp(self.norm2(x))
        
        return x


class HybridVGGViT(nn.Module):
    """
    Hybrid model combining VGG convolutional layers with Vision Transformer.
    
    Architecture:
    1. VGG feature extractor (4 blocks)
    2. Patch embedding layer
    3. Vision Transformer blocks (6 layers)
    4. Classification head
    """
    
    def __init__(
        self,
        num_classes: int = 1000,
        input_channels: int = 3,
        embed_dim: int = 768,
        num_heads: int = 12,
        num_transformer_layers: int = 6,
        mlp_ratio: float = 4.0,
        dropout: float = 0.1
    ):
        super(HybridVGGViT, self).__init__()
        
        # VGG feature extractor
        self.vgg_features = VGGFeatureExtractor(input_channels)
        
        # Patch embedding
        self.patch_embed = PatchEmbedding(feature_dim=512, embed_dim=embed_dim)
        
        # Transformer blocks
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, mlp_ratio, dropout)
            for _ in range(num_transformer_layers)
        ])
        
        # Classification head
        self.norm = nn.LayerNorm(embed_dim)
        self.head = nn.Linear(embed_dim, num_classes)
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        """Initialize model weights."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
                
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # VGG feature extraction
        features = self.vgg_features(x)  # (B, 512, 14, 14)
        
        # Convert to patch embeddings
        x = self.patch_embed(features)   # (B, num_patches+1, embed_dim)
        
        # Apply transformer blocks
        for block in self.transformer_blocks:
            x = block(x)
        
        # Classification using CLS token
        x = self.norm(x)
        cls_token = x[:, 0]  # Extract CLS token
        logits = self.head(cls_token)
        
        return logits
    
    def get_feature_maps(self, x: torch.Tensor) -> torch.Tensor:
        """Extract VGG feature maps for visualization."""
        return self.vgg_features(x)
    
    def get_attention_weights(self, x: torch.Tensor) -> list:
        """Extract attention weights from transformer blocks."""
        features = self.vgg_features(x)
        x = self.patch_embed(features)
        
        attention_weights = []
        for block in self.transformer_blocks:
            # This would require modifying the attention module to return weights
            # For now, just return empty list
            pass
        
        return attention_weights


def create_hybrid_vgg_vit(
    num_classes: int = 1000,
    input_size: Tuple[int, int, int] = (3, 224, 224),
    embed_dim: int = 768,
    num_transformer_layers: int = 6,
    num_heads: int = 12,
    mlp_ratio: float = 4.0,
    dropout: float = 0.1,
    validate_config: bool = True
) -> HybridVGGViT:
    """
    Factory function to create a Hybrid VGG-ViT model with validation.

    Args:
        num_classes: Number of output classes
        input_size: Input tensor size (C, H, W)
        embed_dim: Transformer embedding dimension
        num_transformer_layers: Number of transformer blocks
        num_heads: Number of attention heads
        mlp_ratio: MLP expansion ratio
        dropout: Dropout probability
        validate_config: Whether to validate configuration

    Returns:
        HybridVGGViT model instance

    Raises:
        ConfigurationError: If configuration is invalid

    Example:
        >>> # Standard configuration
        >>> model = create_hybrid_vgg_vit(num_classes=1000)

        >>> # Small configuration
        >>> model = create_hybrid_vgg_vit(
        ...     num_classes=100,
        ...     embed_dim=384,
        ...     num_heads=6,
        ...     num_transformer_layers=4
        ... )
    """
    input_channels, height, width = input_size

    # Validate input size
    if height != 224 or width != 224:
        raise ConfigurationError(
            f"Model expects 224x224 input images, got {height}x{width}. "
            f"Please use input_size=(3, 224, 224) or resize your images."
        )

    # Validate configuration if requested
    if validate_config:
        validate_model_config(
            embed_dim=embed_dim,
            num_heads=num_heads,
            num_transformer_layers=num_transformer_layers,
            num_classes=num_classes,
            input_channels=input_channels
        )

    # Create model with validated parameters
    try:
        model = HybridVGGViT(
            num_classes=num_classes,
            input_channels=input_channels,
            embed_dim=embed_dim,
            num_heads=num_heads,
            num_transformer_layers=num_transformer_layers,
            mlp_ratio=mlp_ratio,
            dropout=dropout
        )
    except Exception as e:
        raise ConfigurationError(f"Failed to create model: {e}")

    return model


def get_recommended_configs() -> Dict[str, Dict[str, Any]]:
    """
    Get recommended model configurations for different use cases.

    Returns:
        Dictionary of configuration names and their parameters
    """
    return {
        "tiny": {
            "embed_dim": 192,
            "num_heads": 3,
            "num_transformer_layers": 4,
            "description": "Tiny model for fast inference (~5M parameters)"
        },
        "small": {
            "embed_dim": 384,
            "num_heads": 6,
            "num_transformer_layers": 4,
            "description": "Small model for resource-constrained environments (~22M parameters)"
        },
        "base": {
            "embed_dim": 768,
            "num_heads": 12,
            "num_transformer_layers": 6,
            "description": "Base model for standard use cases (~86M parameters)"
        },
        "large": {
            "embed_dim": 1024,
            "num_heads": 16,
            "num_transformer_layers": 12,
            "description": "Large model for high accuracy requirements (~307M parameters)"
        }
    }


def create_model_from_config(config_name: str, num_classes: int = 1000) -> HybridVGGViT:
    """
    Create model from predefined configuration.

    Args:
        config_name: Name of configuration ("tiny", "small", "base", "large")
        num_classes: Number of output classes

    Returns:
        HybridVGGViT model instance

    Example:
        >>> model = create_model_from_config("small", num_classes=100)
    """
    configs = get_recommended_configs()

    if config_name not in configs:
        available = list(configs.keys())
        raise ConfigurationError(
            f"Unknown configuration '{config_name}'. Available: {available}"
        )

    config = configs[config_name]

    return create_hybrid_vgg_vit(
        num_classes=num_classes,
        embed_dim=config["embed_dim"],
        num_heads=config["num_heads"],
        num_transformer_layers=config["num_transformer_layers"]
    )


if __name__ == "__main__":
    # Example usage
    model = create_hybrid_vgg_vit(num_classes=1000)
    
    # Test forward pass
    x = torch.randn(2, 3, 224, 224)  # Batch of 2 images
    output = model(x)
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
