"""
Hybrid VGG-Vision Transformer Model

This module implements a hybrid deep learning architecture that combines:
1. VGG-style convolutional blocks for feature extraction
2. Vision Transformer blocks for spatial relationship modeling
3. Classification head for final predictions

Author: AI Assistant
Framework: PyTorch
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Tuple, Optional


class VGGBlock(nn.Module):
    """
    VGG-style convolutional block with 3x3 convolutions, ReLU activation, and max pooling.
    """
    
    def __init__(self, in_channels: int, out_channels: int, num_convs: int = 2):
        super(VGGBlock, self).__init__()
        
        layers = []
        for i in range(num_convs):
            layers.extend([
                nn.Conv2d(
                    in_channels if i == 0 else out_channels,
                    out_channels,
                    kernel_size=3,
                    padding=1,
                    bias=False
                ),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ])
        
        layers.append(nn.MaxPool2d(kernel_size=2, stride=2))
        self.block = nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.block(x)


class VGGFeatureExtractor(nn.Module):
    """
    VGG-style feature extractor with multiple convolutional blocks.
    """
    
    def __init__(self, input_channels: int = 3):
        super(VGGFeatureExtractor, self).__init__()
        
        # VGG blocks with increasing channel dimensions
        self.block1 = VGGBlock(input_channels, 64, num_convs=2)    # 224x224 -> 112x112
        self.block2 = VGGBlock(64, 128, num_convs=2)              # 112x112 -> 56x56
        self.block3 = VGGBlock(128, 256, num_convs=3)             # 56x56 -> 28x28
        self.block4 = VGGBlock(256, 512, num_convs=3)             # 28x28 -> 14x14
        
        # Output: 512 channels, 14x14 spatial dimensions
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.block1(x)
        x = self.block2(x)
        x = self.block3(x)
        x = self.block4(x)
        return x


class PatchEmbedding(nn.Module):
    """
    Converts CNN feature maps to patch embeddings for Vision Transformer.
    """
    
    def __init__(self, feature_dim: int = 512, embed_dim: int = 768, patch_size: int = 2):
        super(PatchEmbedding, self).__init__()
        
        self.patch_size = patch_size
        self.embed_dim = embed_dim
        
        # Project patches to embedding dimension
        self.projection = nn.Conv2d(
            feature_dim, 
            embed_dim, 
            kernel_size=patch_size, 
            stride=patch_size
        )
        
        # Learnable position embeddings
        # For 14x14 input with patch_size=2, we get 7x7 = 49 patches
        self.num_patches = (14 // patch_size) ** 2
        self.position_embeddings = nn.Parameter(
            torch.randn(1, self.num_patches + 1, embed_dim)  # +1 for CLS token
        )
        
        # CLS token
        self.cls_token = nn.Parameter(torch.randn(1, 1, embed_dim))
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch_size = x.shape[0]
        
        # Convert to patches: (B, C, H, W) -> (B, embed_dim, H//patch_size, W//patch_size)
        x = self.projection(x)
        
        # Flatten patches: (B, embed_dim, H', W') -> (B, embed_dim, H'*W')
        x = x.flatten(2)
        
        # Transpose: (B, embed_dim, num_patches) -> (B, num_patches, embed_dim)
        x = x.transpose(1, 2)
        
        # Add CLS token
        cls_tokens = self.cls_token.expand(batch_size, -1, -1)
        x = torch.cat([cls_tokens, x], dim=1)
        
        # Add position embeddings
        x = x + self.position_embeddings
        
        return x


class MultiHeadSelfAttention(nn.Module):
    """
    Multi-head self-attention mechanism for Vision Transformer.
    """
    
    def __init__(self, embed_dim: int = 768, num_heads: int = 12, dropout: float = 0.1):
        super(MultiHeadSelfAttention, self).__init__()
        
        assert embed_dim % num_heads == 0
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.scale = self.head_dim ** -0.5
        
        self.qkv = nn.Linear(embed_dim, embed_dim * 3, bias=False)
        self.proj = nn.Linear(embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch_size, seq_len, embed_dim = x.shape
        
        # Generate Q, K, V
        qkv = self.qkv(x).reshape(batch_size, seq_len, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # (3, B, num_heads, seq_len, head_dim)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # Attention computation
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = F.softmax(attn, dim=-1)
        attn = self.dropout(attn)
        
        # Apply attention to values
        x = (attn @ v).transpose(1, 2).reshape(batch_size, seq_len, embed_dim)
        x = self.proj(x)
        
        return x


class TransformerBlock(nn.Module):
    """
    Vision Transformer block with multi-head attention and MLP.
    """
    
    def __init__(self, embed_dim: int = 768, num_heads: int = 12, mlp_ratio: float = 4.0, dropout: float = 0.1):
        super(TransformerBlock, self).__init__()
        
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = MultiHeadSelfAttention(embed_dim, num_heads, dropout)
        
        self.norm2 = nn.LayerNorm(embed_dim)
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(mlp_hidden_dim, embed_dim),
            nn.Dropout(dropout)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Multi-head self-attention with residual connection
        x = x + self.attn(self.norm1(x))
        
        # MLP with residual connection
        x = x + self.mlp(self.norm2(x))
        
        return x


class HybridVGGViT(nn.Module):
    """
    Hybrid model combining VGG convolutional layers with Vision Transformer.
    
    Architecture:
    1. VGG feature extractor (4 blocks)
    2. Patch embedding layer
    3. Vision Transformer blocks (6 layers)
    4. Classification head
    """
    
    def __init__(
        self,
        num_classes: int = 1000,
        input_channels: int = 3,
        embed_dim: int = 768,
        num_heads: int = 12,
        num_transformer_layers: int = 6,
        mlp_ratio: float = 4.0,
        dropout: float = 0.1
    ):
        super(HybridVGGViT, self).__init__()
        
        # VGG feature extractor
        self.vgg_features = VGGFeatureExtractor(input_channels)
        
        # Patch embedding
        self.patch_embed = PatchEmbedding(feature_dim=512, embed_dim=embed_dim)
        
        # Transformer blocks
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads, mlp_ratio, dropout)
            for _ in range(num_transformer_layers)
        ])
        
        # Classification head
        self.norm = nn.LayerNorm(embed_dim)
        self.head = nn.Linear(embed_dim, num_classes)
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        """Initialize model weights."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
                
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # VGG feature extraction
        features = self.vgg_features(x)  # (B, 512, 14, 14)
        
        # Convert to patch embeddings
        x = self.patch_embed(features)   # (B, num_patches+1, embed_dim)
        
        # Apply transformer blocks
        for block in self.transformer_blocks:
            x = block(x)
        
        # Classification using CLS token
        x = self.norm(x)
        cls_token = x[:, 0]  # Extract CLS token
        logits = self.head(cls_token)
        
        return logits
    
    def get_feature_maps(self, x: torch.Tensor) -> torch.Tensor:
        """Extract VGG feature maps for visualization."""
        return self.vgg_features(x)
    
    def get_attention_weights(self, x: torch.Tensor) -> list:
        """Extract attention weights from transformer blocks."""
        features = self.vgg_features(x)
        x = self.patch_embed(features)
        
        attention_weights = []
        for block in self.transformer_blocks:
            # This would require modifying the attention module to return weights
            # For now, just return empty list
            pass
        
        return attention_weights


def create_hybrid_vgg_vit(
    num_classes: int = 1000,
    input_size: Tuple[int, int, int] = (3, 224, 224),
    embed_dim: int = 768,
    num_transformer_layers: int = 6
) -> HybridVGGViT:
    """
    Factory function to create a Hybrid VGG-ViT model.
    
    Args:
        num_classes: Number of output classes
        input_size: Input tensor size (C, H, W)
        embed_dim: Transformer embedding dimension
        num_transformer_layers: Number of transformer blocks
        
    Returns:
        HybridVGGViT model instance
    """
    model = HybridVGGViT(
        num_classes=num_classes,
        input_channels=input_size[0],
        embed_dim=embed_dim,
        num_transformer_layers=num_transformer_layers
    )
    
    return model


if __name__ == "__main__":
    # Example usage
    model = create_hybrid_vgg_vit(num_classes=1000)
    
    # Test forward pass
    x = torch.randn(2, 3, 224, 224)  # Batch of 2 images
    output = model(x)
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
