# Core deep learning framework
torch>=2.0.0
torchvision>=0.15.0

# Data manipulation and numerical computing
numpy>=1.21.0
pandas>=1.3.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Image processing
Pillow>=8.3.0
opencv-python>=4.5.0

# Progress bars and utilities
tqdm>=4.62.0

# Scientific computing
scipy>=1.7.0

# Optional: For advanced features
# tensorboard>=2.8.0  # For training visualization
# wandb>=0.12.0       # For experiment tracking
# timm>=0.6.0         # For additional model components
# einops>=0.4.0       # For tensor operations

# Development and testing (optional)
# pytest>=6.2.0
# black>=21.0.0
# flake8>=3.9.0
